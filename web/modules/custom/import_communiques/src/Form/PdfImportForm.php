<?php

namespace Drupal\import_communiques\Form;

use <PERSON><PERSON>al\Core\Form\FormBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON><PERSON>\node\Entity\Node;
use <PERSON><PERSON>al\Core\File\FileSystemInterface;
use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use Drupal\file\Entity\File;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Provides a form for importing PDF files to link with existing press releases.
 */
class PdfImportForm extends FormBase {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The file system service.
   *
   * @var \Drupal\Core\File\FileSystemInterface
   */
  protected $fileSystem;

  /**
   * Constructs a new PdfImportForm.
   */
  public function __construct(
    EntityTypeManagerInterface $entity_type_manager,
    FileSystemInterface $file_system
  ) {
    $this->entityTypeManager = $entity_type_manager;
    $this->fileSystem = $file_system;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('entity_type.manager'),
      $container->get('file_system')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'import_communiques_pdf_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $form['description'] = [
      '#type' => 'markup',
      '#markup' => '<p>' . $this->t('Importez plusieurs fichiers PDF qui seront automatiquement liés aux communiqués de presse existants basés sur leur titre.') . '</p>',
    ];

    $form['pdf_files'] = [
      '#type' => 'managed_file',
      '#title' => $this->t('Fichiers PDF'),
      '#description' => $this->t('Sélectionnez un ou plusieurs fichiers PDF. Les noms des fichiers doivent correspondre aux titres des communiqués de presse existants.'),
      '#upload_location' => 'public://communiques_pdf/',
      '#upload_validators' => [
        'file_validate_extensions' => ['pdf'],
        'file_validate_size' => [50 * 1024 * 1024], // 50MB max
      ],
      '#multiple' => TRUE,
      '#required' => TRUE,
    ];

    $form['matching_mode'] = [
      '#type' => 'radios',
      '#title' => $this->t('Mode de correspondance'),
      '#description' => $this->t('Choisissez comment faire correspondre les fichiers PDF avec les communiqués de presse.'),
      '#options' => [
        'exact' => $this->t('Correspondance exacte du titre'),
        'partial' => $this->t('Correspondance partielle (contient le nom du fichier)'),
      ],
      '#default_value' => 'partial',
      '#required' => TRUE,
    ];

    $form['language'] = [
      '#type' => 'select',
      '#title' => $this->t('Langue des communiqués'),
      '#description' => $this->t('Langue des communiqués de presse à rechercher.'),
      '#options' => [
        'fr' => $this->t('Français'),
        'ar' => $this->t('Arabe'),
        'all' => $this->t('Toutes les langues'),
      ],
      '#default_value' => 'fr',
      '#required' => TRUE,
    ];

    $form['actions']['#type'] = 'actions';
    $form['actions']['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Importer les fichiers PDF'),
      '#button_type' => 'primary',
    ];

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $pdf_files = $form_state->getValue('pdf_files');
    $matching_mode = $form_state->getValue('matching_mode');
    $language = $form_state->getValue('language');

    if (empty($pdf_files)) {
      $this->messenger()->addError($this->t('Aucun fichier PDF sélectionné.'));
      return;
    }

    $batch = [
      'title' => $this->t('Importing PDF files...'),
      'operations' => [],
      'finished' => [$this, 'batchFinished'],
      'progress_message' => $this->t('Processed @current out of @total files.'),
    ];

    foreach ($pdf_files as $file_id) {
      $batch['operations'][] = [
        [$this, 'processPdfFile'],
        [$file_id, $matching_mode, $language],
      ];
    }

    batch_set($batch);
  }

  /**
   * Processes a single PDF file for batch operations.
   */
  public function processPdfFile($file_id, $matching_mode, $language, &$context) {
    // Initialiser le contexte de manière robuste
    if (!isset($context['results']) || !is_array($context['results'])) {
      $context['results'] = [
        'success' => 0,
        'skipped' => 0,
        'errors' => 0,
        'messages' => [],
      ];
    }

    // S'assurer que toutes les clés existent
    $context['results']['success'] = $context['results']['success'] ?? 0;
    $context['results']['skipped'] = $context['results']['skipped'] ?? 0;
    $context['results']['errors'] = $context['results']['errors'] ?? 0;
    $context['results']['messages'] = $context['results']['messages'] ?? [];

    try {
      // Charger le fichier
      $file = File::load($file_id);
      if (!$file) {
        $context['results']['errors']++;
        $context['results']['messages'][] = $this->t('Fichier avec ID @id non trouvé.', ['@id' => $file_id]);
        return;
      }

      $filename = $file->getFilename();
      $filename_without_ext = pathinfo($filename, PATHINFO_FILENAME);

      // Rechercher les communiqués de presse correspondants
      $matching_nodes = $this->findMatchingNodes($filename_without_ext, $matching_mode, $language);

      if (empty($matching_nodes)) {
        $context['results']['skipped']++;
        $context['results']['messages'][] = $this->t('Aucun communiqué de presse trouvé pour le fichier: @filename', ['@filename' => $filename]);
        $context['message'] = $this->t('Skipping @filename - no matching press release found', ['@filename' => $filename]);
        return;
      }

      // Attacher le fichier aux nœuds trouvés
      $attached_count = 0;
      foreach ($matching_nodes as $node) {
        if ($this->attachPdfToNode($file, $node)) {
          $attached_count++;
        }
      }

      if ($attached_count > 0) {
        $context['results']['success']++;
        $context['results']['messages'][] = $this->t('Fichier @filename attaché à @count communiqué(s) de presse.', [
          '@filename' => $filename,
          '@count' => $attached_count,
        ]);
      } else {
        $context['results']['errors']++;
        $context['results']['messages'][] = $this->t('Erreur lors de l\'attachement du fichier: @filename', ['@filename' => $filename]);
      }

      $context['message'] = $this->t('Processing @filename', ['@filename' => $filename]);

    } catch (\Exception $e) {
      $context['results']['errors']++;
      $context['results']['messages'][] = $this->t('Erreur lors du traitement du fichier @id: @message', [
        '@id' => $file_id,
        '@message' => $e->getMessage(),
      ]);
      \Drupal::logger('import_communiques')->error('Error processing PDF file @id: @message', [
        '@id' => $file_id,
        '@message' => $e->getMessage(),
      ]);
    }
  }

  /**
   * Finds matching press release nodes based on filename.
   */
  private function findMatchingNodes($filename, $matching_mode, $language) {
    $query = $this->entityTypeManager
      ->getStorage('node')
      ->getQuery()
      ->accessCheck(FALSE)
      ->condition('type', 'communiques_de_presse')
      ->condition('status', 1);

    // Filtrer par langue si spécifié
    if ($language !== 'all') {
      $query->condition('langcode', $language);
    }

    if ($matching_mode === 'exact') {
      $query->condition('title', $filename);
    } else {
      // Correspondance partielle
      $query->condition('title', '%' . $filename . '%', 'LIKE');
    }

    $nids = $query->execute();

    if (empty($nids)) {
      return [];
    }

    return $this->entityTypeManager
      ->getStorage('node')
      ->loadMultiple($nids);
  }

  /**
   * Attaches a PDF file to a press release node.
   */
  private function attachPdfToNode(File $file, Node $node) {
    try {
      // Vérifier si le nœud a déjà un fichier attaché
      $existing_file = $node->get('field_lien_telechargement')->entity;
      if ($existing_file) {
        // Optionnel: remplacer le fichier existant ou ignorer
        \Drupal::logger('import_communiques')->info('Node @nid already has a PDF file attached. Replacing with @filename.', [
          '@nid' => $node->id(),
          '@filename' => $file->getFilename(),
        ]);
      }

      // Attacher le fichier au nœud
      $node->set('field_lien_telechargement', [
        'target_id' => $file->id(),
        'description' => $file->getFilename(),
      ]);

      $node->save();
      return TRUE;

    } catch (\Exception $e) {
      \Drupal::logger('import_communiques')->error('Error attaching PDF to node @nid: @message', [
        '@nid' => $node->id(),
        '@message' => $e->getMessage(),
      ]);
      return FALSE;
    }
  }

  /**
   * Batch finished callback.
   */
  public function batchFinished($success, $results) {
    if ($success) {
      $message = $this->t('Import terminé. @success fichiers importés, @skipped ignorés, @errors erreurs.', [
        '@success' => $results['success'] ?? 0,
        '@skipped' => $results['skipped'] ?? 0,
        '@errors' => $results['errors'] ?? 0,
      ]);
      $this->messenger()->addMessage($message);

      // Afficher les messages détaillés
      if (!empty($results['messages'])) {
        foreach ($results['messages'] as $msg) {
          $this->messenger()->addMessage($msg);
        }
      }
    } else {
      $this->messenger()->addError($this->t('Une erreur est survenue pendant l\'import des fichiers PDF.'));
    }
  }
}
